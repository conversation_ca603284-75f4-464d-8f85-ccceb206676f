package org.biosino.lf.mash.mashweb.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 文件路径配置类
 * 
 * <AUTHOR>
 * @date 2025/7/21
 */
@Data
@Component
@ConfigurationProperties(prefix = "file")
public class FileProperties {

    /**
     * 基础数据目录
     */
    private String baseDataDir = "D:\\data\\demo";

    /**
     * 脚本目录
     */
    private String scriptDir = "E:\\IdeaProjects\\mash\\script";

    /**
     * KO数据处理脚本路径
     */
    private String koProcessScript = "process_ko_data.py";

    /**
     * 物种数据处理脚本路径
     */
    private String speciesProcessScript = "process_species_data.py";

    /**
     * KO数据文件路径
     */
    private String koDataFile = "All.KO_Sample.wide.RA.tsv";

    /**
     * KO列表文件路径
     */
    private String koListFile = "KO_list.txt";

    /**
     * RUN_ID列表文件路径
     */
    private String runIdListFile = "RUN_ID_LIST.txt";

    /**
     * 丰度表目录
     */
    private String abundanceTableDir = "Abundance_Table";

    /**
     * 获取完整的脚本路径
     */
    public String getKoProcessScriptPath() {
        return scriptDir + "\\" + koProcessScript;
    }

    /**
     * 获取完整的物种处理脚本路径
     */
    public String getSpeciesProcessScriptPath() {
        return scriptDir + "\\" + speciesProcessScript;
    }

    /**
     * 获取完整的KO数据文件路径
     */
    public String getKoDataFilePath() {
        return baseDataDir + "\\" + koDataFile;
    }

    /**
     * 获取完整的KO列表文件路径
     */
    public String getKoListFilePath() {
        return baseDataDir + "\\" + koListFile;
    }

    /**
     * 获取完整的RUN_ID列表文件路径
     */
    public String getRunIdListFilePath() {
        return baseDataDir + "\\" + runIdListFile;
    }

    /**
     * 获取丰度表文件路径
     */
    public String getAbundanceTableFilePath(String domain, String taxonomy) {
        return baseDataDir + "\\" + abundanceTableDir + "\\" + domain + "." + taxonomy + ".percent.csv";
    }

    /**
     * 获取任务工作目录
     */
    public String getTaskWorkDir(String taskId) {
        return baseDataDir + "\\" + taskId;
    }

    /**
     * 获取任务输入目录
     */
    public String getTaskInputDir(String taskId) {
        return getTaskWorkDir(taskId) + "\\input";
    }

    /**
     * 获取任务输出目录
     */
    public String getTaskOutputDir(String taskId) {
        return getTaskWorkDir(taskId) + "\\output";
    }
}
