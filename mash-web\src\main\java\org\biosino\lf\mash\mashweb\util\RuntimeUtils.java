package org.biosino.lf.mash.mashweb.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RuntimeUtil;
import com.sun.jna.Pointer;
import com.sun.jna.platform.win32.Kernel32;
import com.sun.jna.platform.win32.WinNT;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @date 2024/11/25
 */
@Slf4j
public class RuntimeUtils {

    private final static ConcurrentHashMap<String, Process> processMap = new ConcurrentHashMap<>();

    public static int execForExitCode(String cmd, String taskId) {
        log.info("开始执行命令：{}", cmd);
        Process process;
        if (FileUtil.isWindows()) {
            process = RuntimeUtil.exec("cmd.exe", "/c", cmd);
        } else {
            process = RuntimeUtil.exec("sh", "-c", cmd);
        }
        int exitCode = 0;
        try {
            if (taskId != null) {
                processMap.put(taskId, process);
            }
            ExecutorService executor = Executors.newFixedThreadPool(2);
            executor.submit(() -> consumeStream(process.getInputStream(), "[stdout]"));
            executor.submit(() -> consumeStream(process.getErrorStream(), "[stderr]"));
            executor.shutdown();
            exitCode = process.waitFor();
            log.info("执行命令：{} 完成，退出状态码:{}", cmd, exitCode);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (taskId != null) {
                processMap.remove(taskId);
            }
        }
        return exitCode;
    }

    public static int killProcess(String taskId) {
        Process process = processMap.get(taskId);
        if (process != null) {
            log.info("正在终止任务: {}", taskId);

            try {
                // 尝试杀死进程树（包括所有子进程）
                killProcessTree(process);
                process.destroyForcibly();

                log.info("任务 {} 已成功终止", taskId);
            } catch (Exception e) {
                log.error("终止任务 {} 时发生错误: {}", taskId, e.getMessage(), e);
                // 如果出现异常，仍然尝试强制终止
                process.destroyForcibly();
            } finally {
                processMap.remove(taskId); // 确保清理
            }
            return 0; // 成功
        } else {
            log.warn("未找到任务 ID: {}", taskId);
            return -1; // 任务 ID 不存在
        }
    }

    public static int execForExitCode(String cmd) {
        return execForExitCode(cmd, null);
    }

    private static void consumeStream(InputStream inputStream, String prefix) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            while (reader.readLine() != null) ; // 只读取不打印，防止阻塞
        } catch (IOException e) {
            log.error("读取进程输出流时出错", e);
        }
    }

    /**
     * 杀死进程树（包括所有子进程）
     * 在Java 8中，Process.destroyForcibly()只能杀死主进程，无法杀死子进程
     * 此方法通过操作系统命令来杀死整个进程树
     *
     * @param process 要杀死的进程
     */
    private static void killProcessTree(Process process) {
        try {
            long pid = getPid(process);
            if (pid > 0) {
                log.info("获取到进程PID: {}", pid);

                if (FileUtil.isWindows()) {
                    killProcessTreeWindows(pid);
                } else {
                    killProcessTreeUnix(pid);
                }
            } else {
                log.warn("无法获取进程PID，使用默认方式终止进程");
                process.destroyForcibly();
            }
        } catch (Exception e) {
            log.error("杀死进程树时发生错误: {}", e.getMessage(), e);
            // 如果出现异常，回退到默认方式
            process.destroyForcibly();
        }
    }

    /**
     * 获取进程ID
     * 在Java 8中没有Process.pid()方法，需要通过反射获取
     *
     * @param process 进程对象
     * @return 进程ID，如果获取失败返回-1
     */
    private static long getPid(Process process) throws NoSuchFieldException, IllegalAccessException {
        Field field = process.getClass().getDeclaredField("handle");
        field.setAccessible(true);
        long handle = field.getLong(process);
        Kernel32 kernel = Kernel32.INSTANCE;
        WinNT.HANDLE winntHandle = new WinNT.HANDLE();
        winntHandle.setPointer(Pointer.createConstant(handle));
        long pid = kernel.GetProcessId(winntHandle);
        log.info("获取到进程id：{}", pid);
        return pid;
    }

    /**
     * 在Windows系统上杀死进程树
     *
     * @param pid 进程ID或handle
     */
    private static void killProcessTreeWindows(long pid) {
        try {
            // 使用taskkill命令杀死进程树
            // /F 强制终止进程
            // /T 终止指定的进程和由它启用的子进程
            String command = String.format("taskkill /F /T /PID %d", pid);
            log.info("执行Windows杀死进程树命令: {}", command);

            Process killProcess = RuntimeUtil.exec("cmd.exe", "/c", command);
            int exitCode = killProcess.waitFor();

            if (exitCode == 0) {
                log.info("成功杀死Windows进程树，PID: {}", pid);
            } else {
                log.warn("杀死Windows进程树失败，退出码: {}, PID: {}", exitCode, pid);
            }
        } catch (Exception e) {
            log.error("在Windows上杀死进程树时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 在Unix/Linux系统上杀死进程树
     *
     * @param pid 进程ID
     */
    private static void killProcessTreeUnix(long pid) {
        try {
            String command = String.format("kill %d", pid);
            log.info("执行linux杀死进程树命令: {}", command);

            Process killProcess = RuntimeUtil.exec("sh", "-c", command);
            int exitCode = killProcess.waitFor();

            if (exitCode == 0) {
                log.info("成功杀死linux进程树，PID: {}", pid);
            } else {
                log.warn("杀死linux进程树失败，退出码: {}, PID: {}", exitCode, pid);
            }
        } catch (Exception e) {
            log.error("在linux上杀死进程树时发生错误: {}", e.getMessage(), e);
        }
    }

}
